// reset 因在 ide 里 插件样式错乱的问题
import './media/aiAssistant-reset.css';
// 这个是 ai 助手的一些额外的需求样式，比如登录
import './media/aiAssistant.css';
import * as nls from '../../../../nls.js';
import {
	registerAction2,
	Action2,
	MenuId,
} from '../../../../platform/actions/common/actions.js';
import {
	IViewContainersRegistry,
	ViewContainerLocation,
	Extensions as ViewExtensions,
	IViewsRegistry,
	IViewDescriptorService,
} from '../../../../workbench/common/views.js';
import { SyncDescriptor } from '../../../../platform/instantiation/common/descriptors.js';
import { Registry } from '../../../../platform/registry/common/platform.js';
import { DisposableStore, IDisposable } from '../../../../base/common/lifecycle.js';
import { IInstantiationService, ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';
import { ContextKeyExpr, IContextKeyService, RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';
import { KeybindingWeight } from '../../../../platform/keybinding/common/keybindingsRegistry.js';
import { KeyCode, KeyMod } from '../../../../base/common/keyCodes.js';
import { IViewsService } from '../../../../workbench/services/views/common/viewsService.js';
import { ViewPaneContainer } from '../../../browser/parts/views/viewPaneContainer.js';
import { Orientation } from '../../../../base/browser/ui/sash/sash.js';
import * as DOM from '../../../../base/browser/dom.js';
import { Codicon } from '../../../../base/common/codicons.js';
import { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';
import { ViewPane } from '../../../browser/parts/views/viewPane.js';
import { IThemeService, ApiThemeClassName } from '../../../../platform/theme/common/themeService.js';
import { IContextMenuService } from '../../../../platform/contextview/browser/contextView.js';
import { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';
import { IKeybindingService } from '../../../../platform/keybinding/common/keybinding.js';
import { IOpenerService } from '../../../../platform/opener/common/opener.js';
import { IHoverService } from '../../../../platform/hover/browser/hover.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { IWorkbenchLayoutService, Parts } from '../../../../workbench/services/layout/browser/layoutService.js';
// @ts-ignore
// eslint-disable-next-line
import { mountApp } from '../../../../../vs/kwaipilot/webview-ui/dist-export/mount-export.js';
// eslint-disable-next-line
import '../../../../../vs/kwaipilot/webview-ui/dist-export/style.css';
import { ICommandService, CommandsRegistry } from '../../../../platform/commands/common/commands.js';
import { registerWorkbenchContribution2, IWorkbenchContribution, WorkbenchPhase } from '../../../../workbench/common/contributions.js';
import { IStorageService, StorageScope } from '../../../../platform/storage/common/storage.js';
import BridgeImpl from './BridgeImpl.js';
import { USER_INFO_KEY } from '../../../../base/common/kwaipilot/login.js';
import { safeInnerHtml } from '../../../../base/browser/dom.js';
import { ILoginService } from '../../login/browser/login.contribution.js';
import { interceptFetch, interceptHTMLDataset, interceptHTMLSetting, overrideCssRule } from './utils/dom-proxy.js';
import { getRadarInstance } from '../../../browser/radar.js';
import { IUserInfoWatcherService, UserInfoWatcherService } from './userInfoWatcherService.js';
import { ColorScheme } from '../../../../platform/theme/common/theme.js';

// 定义接口
export interface IAuthState {
	isLoggedIn: boolean;
	user?: {
		id: string;
		name: string;
		email: string;
		avatar?: string;
	};
}

// 容器ID
export const AI_ASSISTANT_VIEW_CONTAINER_ID = 'workbench.view.aiAssistant';
export const AI_ASSISTANT_PANEL_ID = 'workbench.view.aiAssistant.chat';

// 定义图标
export const aiAssistantIcon = registerIcon(
	'ai-assistant-icon',
	Codicon.sparkle,
	nls.localize('aiAssistantIcon', 'AI Assistant Icon'),
);

// 定义 AIAssistantPanel 类
export class AIAssistantPanel extends ViewPane {
	static readonly ID = AI_ASSISTANT_PANEL_ID;
	/** 模拟 dom 的 html 容器 */
	private htmlContainer: HTMLDivElement | undefined;
	/** 模拟 dom 的 body 容器 */
	private bodyContainer: HTMLDivElement | undefined;
	/** 模拟 react 的 root 容器 */
	private rootContainer: HTMLDivElement | undefined;
	private commandDisposable: IDisposable | undefined;
	// 添加消息监听器存储
	private _messageListeners: Map<Function, IDisposable> | undefined;
	// 添加主题相关属性
	private currentThemeId: string | undefined;
	private currentThemeType: ColorScheme | undefined;
	// 添加应用挂载状态标识
	private isAppMounted: boolean = false;
	// 添加DOM拦截器状态标识
	private isDOMInterceptorApplied: boolean = false;
	private bridgeImpl: BridgeImpl;
	private userInfoWatcherService: IUserInfoWatcherService;

	constructor(
		options: any,
		@IKeybindingService keybindingService: IKeybindingService,
		@IContextMenuService contextMenuService: IContextMenuService,
		@IConfigurationService protected override readonly configurationService: IConfigurationService,
		@IContextKeyService protected override contextKeyService: IContextKeyService,
		@IViewDescriptorService viewDescriptorService: IViewDescriptorService,
		@IInstantiationService protected override readonly instantiationService: IInstantiationService,
		@IOpenerService openerService: IOpenerService,
		@IThemeService protected override readonly themeService: IThemeService,
		@IHoverService hoverService: IHoverService,
		@ILogService private readonly logService: ILogService,
		@ICommandService private readonly commandService: ICommandService,
		@IStorageService private readonly storageService: IStorageService,
	) {
		super(
			options,
			keybindingService,
			contextMenuService,
			configurationService,
			contextKeyService,
			viewDescriptorService,
			instantiationService,
			openerService,
			themeService,
			hoverService,
		);

		// 创建基础 Bridge 实现
		this.bridgeImpl = new BridgeImpl(this.commandService);

		// 创建用户信息监听服务
		this.userInfoWatcherService = new UserInfoWatcherService(this.storageService);

		// 注册主题变化监听
		this._register(this.themeService.onDidColorThemeChange(() => {
			this.updateThemeOnBody();
		}));

		// 初始化主题
		this.updateThemeOnBody();

		// 订阅配置变更
		this._register(this.configurationService.onDidChangeConfiguration(e => {
			if (e.affectsConfiguration('aiAssistant')) {
				this.updateUIFromConfig();
			}
		}));

		// 添加登录状态监听 - 确保登录后重新挂载应用
		this._register(this.storageService.onDidChangeValue(StorageScope.APPLICATION, USER_INFO_KEY, this._store)(() => {
			const userInfo = this.storageService.get(USER_INFO_KEY, StorageScope.APPLICATION);
			this.logService.info('AI Assistant Panel: Login state changed', { hasUserInfo: !!userInfo, isAppMounted: this.isAppMounted });

			// 如果已登录且容器已创建但应用未挂载，则重新挂载
			if (userInfo && this.rootContainer && !this.isAppMounted) {
				this.logService.info('AI Assistant Panel: Re-mounting app after login');
				this.mountUI();
			}
		}));

	}

	override setVisible(visible: boolean): void {
		super.setVisible(visible);

		if (visible) {
			this.logService.info('AI Assistant Panel: setVisible(true) called', {
				isAppMounted: this.isAppMounted,
				hasRootContainer: !!this.rootContainer
			});

			// 如果视图变得可见，但应用未挂载，则尝试挂载
			const userInfo = this.storageService.get(USER_INFO_KEY, StorageScope.APPLICATION);
			if (userInfo && this.rootContainer && !this.isAppMounted) {
				this.logService.info('AI Assistant Panel: Re-mounting app on setVisible');
				this.mountUI();
			}
		}
	}

	// 从配置更新UI
	private updateUIFromConfig(): void {
		if (!this.bodyContainer) return;
	}
	override dispose(): void {
		// 清理命令注册
		if (this.commandDisposable) {
			this.commandDisposable.dispose();
			this.commandDisposable = undefined;
		}

		// 清理所有消息监听器
		if (this._messageListeners) {
			for (const disposable of this._messageListeners.values()) {
				disposable.dispose();
			}
			this._messageListeners.clear();
			this._messageListeners = undefined;
		}

		// 清理应用挂载状态
		if (this.isAppMounted && this.rootContainer) {
			this.logService.info('AI Assistant Panel: Cleaning up mounted app on dispose');
			// 使用DOM方法清理，避免Trusted Types问题
			while (this.rootContainer.firstChild) {
				this.rootContainer.removeChild(this.rootContainer.firstChild);
			}
			this.isAppMounted = false;
		}

		// 清理桥接实现
		if (this.bridgeImpl) {
			this.bridgeImpl.dispose();
		}

		// 清理用户信息监听服务
		if (this.userInfoWatcherService) {
			this.userInfoWatcherService.dispose();
		}

		// 重置DOM拦截器状态
		this.isDOMInterceptorApplied = false;

		// 清理容器引用
		this.htmlContainer = undefined;
		this.bodyContainer = undefined;
		this.rootContainer = undefined;

		super.dispose();
	}


	protected override renderBody(parent: HTMLElement): void {
		try {
			this.logService.info('AI Assistant Panel: renderBody called');
			super.renderBody(parent);

			// 需要创建一个 root 来模拟 插件的原始结构
			/**
			 * <div class='ai-assistant-container'> // 不要删除，用来模拟 <!Document> 元素 :root
			 * 		<div data-css-scope data-element-type='html'> // 不要删除，这个是专门用来 scope-css 用的
			 * 				<div data-element-type='body'>  // 这个用来 模拟 插件原来的 环境 body 元素
			 * 					<div data-element-type='root'/> // 这个用来 模拟 插件原来的 环境 react-root 元素
			 * 				</div>
			 * 		</div>
			 * </div>
			 */

			// <fale-doc/>
			const wrapper = DOM.append<HTMLDivElement>(parent, DOM.$('.ai-assistant-container'));

			// <fake-html/>
			this.htmlContainer = document.createElement('div');
			this.htmlContainer.dataset.scope = 'true';
			wrapper.appendChild(this.htmlContainer);
			// data-element-type='html'
			this.htmlContainer.dataset.elementType = 'html';
			// <fake-body/>
			this.bodyContainer = document.createElement('div');
			// data-element-type='body'
			this.bodyContainer.dataset.elementType = 'body';
			this.rootContainer = document.createElement('div');
			// <fake-react-root/>
			// data-element-type='root'
			this.rootContainer.dataset.elementType = 'root';
			this.bodyContainer.appendChild(this.rootContainer);
			this.htmlContainer.appendChild(this.bodyContainer);

			this.logService.info('AI Assistant Panel: DOM structure created, calling mountUI');
			this.mountUI();
		} catch (error) {
			this.logService.error('Error rendering AI Assistant panel', error);
		}
	}

	private mountUI() {
		if (!this.bodyContainer || !this.rootContainer) {
			this.logService.warn('AI Assistant Panel: Cannot mount UI - containers not ready');
			return;
		}

		// 如果应用已经挂载，先清理
		if (this.isAppMounted) {
			this.logService.info('AI Assistant Panel: App already mounted, cleaning up first');
			// 使用DOM方法清理，避免Trusted Types问题
			while (this.rootContainer.firstChild) {
				this.rootContainer.removeChild(this.rootContainer.firstChild);
			}
			this.isAppMounted = false;
		}

		try {
			// 同步当前主题到容器
			if (this.currentThemeId) {
				this.bodyContainer.setAttribute('data-vscode-theme-id', this.currentThemeId);
				this.bodyContainer.setAttribute('data-vscode-theme-type', this.currentThemeType || '');
			}

			const win = DOM.getWindow(this.bodyContainer);
			// 标识是否在 IDE 环境中，给插件使用
			// @ts-ignore
			win.__KWAIPILOT_ENV__IN_IDE__ = true;
			// @ts-ignore
			// 标识当前主题类型 在初始化的时候，插件进行使用
			win.__KWAIPILOT_ENV__THEME_TYPE__ = this.currentThemeType;

			// 只在第一次挂载时应用DOM拦截器，避免重复应用
			if (!this.isDOMInterceptorApplied) {
				this.logService.info('AI Assistant Panel: Applying DOM interceptors');
				// 拦截 color-scheme 设置 ，防止污染ide的 其他扩展插件的展示
				interceptHTMLSetting('/out/vs/kwaipilot/webview-ui', win);
				// 拦截 设置 html 的 data-theme 属性，防止污染ide的 其他扩展插件的展示
				if (this.htmlContainer) { interceptHTMLDataset('/out/vs/kwaipilot/webview-ui', win, this.htmlContainer); }
				// 拦截 插件的 style 注入，防止污染 ide 的样式
				overrideCssRule('/out/vs/kwaipilot/webview-ui', '.ai-assistant-container');
				// @TMP 拦截 fetch 统一用法 ，先 fix radar sdk 的bug
				interceptFetch('/out/vs/kwaipilot/webview-ui', win);
				this.isDOMInterceptorApplied = true;
			}

			this.logService.info('AI Assistant Panel: Mounting app to root container');

			// 预先准备好具体的 VSCode 服务实例
			const servicesAccessor = {
				// 命令服务
				commandService: this.commandService,

				// 配置服务
				configurationService: this.configurationService,

				// 日志服务
				logService: this.logService,

				// 主题服务
				themeService: this.themeService,

				// 存储服务
				storageService: this.storageService,

				// 用户信息监听服务
				userInfoWatcherService: this.userInfoWatcherService,

				radar: getRadarInstance(),

				// 通用服务获取器（备用方案）
				getService: <T>(serviceToken: any): T | undefined => {
					try {
						return this.instantiationService.invokeFunction(accessor => accessor.get<T>(serviceToken));
					} catch (error) {
						this.logService.warn('Failed to get service via getService:', serviceToken, error);
						return undefined;
					}
				}
			};
			mountApp(this.rootContainer, this.bridgeImpl, servicesAccessor);

			getRadarInstance().customStage(
				'mountApp',
				{
					timestamp: Date.now(),
					sendTimeline: true,
				}
			);
			this.isAppMounted = true;
			this.logService.info('AI Assistant Panel: App mounted successfully');
		} catch (error) {
			this.logService.error('AI Assistant Panel: Error mounting app', error);
			this.isAppMounted = false;
		}
	}

	protected override layoutBody(height: number, width: number): void {
		try {
			super.layoutBody(height, width);

			if (this.bodyContainer) {
				this.bodyContainer.style.height = `${height}px`;
				this.bodyContainer.style.width = `${width}px`;
			}
		} catch (error) {
			this.logService.error('Error in AIAssistantPanel layoutBody', error);
		}
	}

	private updateThemeOnBody(): void {
		try {
			const currentTheme = this.themeService.getColorTheme();
			const activeTheme = ApiThemeClassName.fromTheme(currentTheme);
			// @ts-ignore
			this.currentThemeId = currentTheme.settingsId;
			this.currentThemeType = currentTheme.type;

			// 获取完整的主题名称
			// @ts-ignore
			const themeLabel = currentTheme.settingsId || currentTheme.label;

			// 这里需要模拟 webview 该有的主题属性注入
			if (this.bodyContainer) {
				if (this.currentThemeId) { this.bodyContainer.setAttribute('data-vscode-theme-id', this.currentThemeId); }
				this.bodyContainer.setAttribute('data-vscode-theme-type', this.currentThemeType);
				// vscode-dark | vscode-light
				this.bodyContainer.setAttribute('data-vscode-theme-kind', activeTheme);
				ApiThemeClassName.getThemeClassNames().forEach(t => {
					this.bodyContainer?.classList.remove(t);
				});
				this.bodyContainer.classList.add(activeTheme);
				// dark | light | hcDark | hcLight
				ApiThemeClassName.getTypeClassNames().forEach(t => {
					this.bodyContainer?.classList.remove(t);
				});
				this.bodyContainer.classList.add(this.currentThemeType);
			}
			this.logService.debug(`Theme updated: ${themeLabel} (${this.currentThemeType})`);
		} catch (error) {
			this.logService.error('Error updating theme:', error);
		}
	}
}

export class UnloginAIAssistantPanel extends ViewPane {
	static readonly ID = 'workbench.view.aiAssistant.chat_no_login';

	constructor(
		options: any,
		@IKeybindingService keybindingService: IKeybindingService,
		@IContextMenuService contextMenuService: IContextMenuService,
		@IConfigurationService protected override readonly configurationService: IConfigurationService,
		@IContextKeyService protected override contextKeyService: IContextKeyService,
		@IViewDescriptorService viewDescriptorService: IViewDescriptorService,
		@IInstantiationService protected override readonly instantiationService: IInstantiationService,
		@IOpenerService openerService: IOpenerService,
		@IThemeService protected override readonly themeService: IThemeService,
		@IHoverService hoverService: IHoverService,
		@ILoginService private readonly loginService: ILoginService) {
		super(
			options,
			keybindingService,
			contextMenuService,
			configurationService,
			contextKeyService,
			viewDescriptorService,
			instantiationService,
			openerService,
			themeService,
			hoverService,
		);
	}

	protected override renderBody(container: HTMLElement): void {
		super.renderBody(container);
		const wrapper = DOM.append(container, DOM.$('.ai-assistant-container'));
		const childHTML = `
		<div class="no_login main-frame">
			<div class="sub-frame">
				<div class="inner-frame">
				<div class="pilot-section">
				<div class="pilot-feature-primary">Collaboration with <span class="pilot-feature-secondary">Kwaipilot</span></div>
				</div>
				<div class="speed-section">Code Smarter, Build Faster</div>
				</div>
			<div class="action-button" data-button-type="login"><div class="button-label" >Log in</div></div>
			</div>
		</div>
		`;
		safeInnerHtml(wrapper, childHTML);
		wrapper.querySelector(`[data-button-type="login"]`)?.addEventListener('click', () => {
			this.loginService.login();
		});
	}
}

export class ShowAuxiliaryBarContribution implements IWorkbenchContribution {
	static readonly ID = 'workbench.contrib.showAuxiliaryBarContribution';
	private readonly disposables = new DisposableStore();

	constructor(
		@IWorkbenchLayoutService private readonly layoutService: IWorkbenchLayoutService,
		@IStorageService private readonly storageService: IStorageService,
		@IContextKeyService private readonly contextKeyService: IContextKeyService,
	) {
		this.initialize();
	}

	registerViews(): void {
		// 注册视图容器和视图 - 添加错误处理
		try {
			const viewContainersRegistry = Registry.as<IViewContainersRegistry>(
				ViewExtensions.ViewContainersRegistry,
			);
			if (!viewContainersRegistry) {
				throw new Error('Failed to get view containers registry');
			}

			// 使用标准的 ViewPaneContainer，不需要自定义容器
			const viewContainer = viewContainersRegistry.registerViewContainer(
				{
					id: AI_ASSISTANT_VIEW_CONTAINER_ID,
					title: nls.localize2('aiAssistant.containerTitle', 'Kwaipilot'),
					icon: aiAssistantIcon,
					ctorDescriptor: new SyncDescriptor(ViewPaneContainer, [
						AI_ASSISTANT_VIEW_CONTAINER_ID,
						{
							mergeViewWithContainerWhenSingleView: true,
							orientation: Orientation.VERTICAL,
						},
					]),
					storageId: AI_ASSISTANT_VIEW_CONTAINER_ID,
					hideIfEmpty: false,
					order: 100,
				},
				ViewContainerLocation.AuxiliaryBar,
			);

			const viewsRegistry = Registry.as<IViewsRegistry>(
				ViewExtensions.ViewsRegistry,
			);
			if (!viewsRegistry) {
				throw new Error('Failed to get views registry');
			}

			const userInfo = this.storageService.get(USER_INFO_KEY, StorageScope.APPLICATION);
			const contextKey = new RawContextKey<boolean>('aiAssistant.isLoggedIn', !!userInfo);
			const boundContextKey = contextKey.bindTo(this.contextKeyService);

			this.disposables.add(
				this.storageService.onDidChangeValue(StorageScope.APPLICATION, USER_INFO_KEY, this.disposables)(() => {
					const userInfo = this.storageService.get(USER_INFO_KEY, StorageScope.APPLICATION);
					boundContextKey.set(!!userInfo);
				})
			);

			viewsRegistry.registerViews(
				[
					// 已登录视图
					{
						id: AIAssistantPanel.ID, // 确保ID与类中的静态ID一致
						name: nls.localize2('aiAssistant.view.name', 'ai chat'),
						ctorDescriptor: new SyncDescriptor(AIAssistantPanel),
						containerIcon: aiAssistantIcon,
						canToggleVisibility: true,
						canMoveView: true,
						when: contextKey,
						collapsed: false,
						// @ts-ignore
						viewContainer: viewContainer,
					},
					// 未登录视图
					{
						id: UnloginAIAssistantPanel.ID,
						name: nls.localize2('aiAssistant.view.name2', 'unlogin'),
						ctorDescriptor: new SyncDescriptor(UnloginAIAssistantPanel),
						containerIcon: aiAssistantIcon,
						canToggleVisibility: true,
						canMoveView: true,
						when: ContextKeyExpr.not(contextKey.key),
						collapsed: false,
						// @ts-ignore
						viewContainer: viewContainer,
					},
				],
				viewContainer,
			);
		} catch (error) {
			console.error(
				'Failed to register AI Assistant view container and views',
				error,
			);
		}
	}

	private async initialize(): Promise<void> {
		this.registerViews();

		setTimeout(() => {
			// 如果配置为 visible 且当前辅助侧边栏不可见，则显示它
			if (!this.layoutService.isVisible(Parts.AUXILIARYBAR_PART)) {
				this.layoutService.setPartHidden(false, Parts.AUXILIARYBAR_PART);
			}
		}, 100);
	}

	dispose(): void {
		this.disposables.dispose();
	}
}

// 注册贡献
registerWorkbenchContribution2(
	ShowAuxiliaryBarContribution.ID,
	ShowAuxiliaryBarContribution,
	WorkbenchPhase.BlockStartup
);


class AIAssistantOpenPanelAction extends Action2 {
	static readonly ID = 'aiAssistant.openPanel';

	constructor() {
		super({
			id: AIAssistantOpenPanelAction.ID,
			title: {
				value: nls.localize('aiAssistant.openPanel', 'Open Kwaipilot'),
				original: 'Open Kwaipilot',
			},
			f1: true,
			category: {

				value: nls.localize('aiAssistant.category', 'Kwaipilot'),
				original: 'Kwaipilot',
			},
			keybinding: {
				weight: KeybindingWeight.WorkbenchContrib,
				primary: KeyMod.CtrlCmd | KeyMod.Shift | KeyCode.KeyK,
			},
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		try {
			const viewsService = accessor.get(IViewsService);
			const logService = accessor.get(ILogService);

			logService.info('AI Assistant Open Panel action triggered');

			// 打开AI助手视图容器并确保AIAssistantPanel面板显示
			await viewsService.openViewContainer(AI_ASSISTANT_VIEW_CONTAINER_ID, true);
			await viewsService.openView(AI_ASSISTANT_PANEL_ID, true);
		} catch (error) {
			console.error('Error in AIAssistantOpenPanelAction.run', error);
		}
	}
}

// 注册设置命令
class OpenAIAssistantSettingsAction extends Action2 {
	constructor() {
		super({
			id: 'aiAssistant.openSettings',
			title: {
				value: nls.localize('aiAssistant.openSettings', 'Settings'),
				original: 'Open AI Assistant Settings',
			},
			category: {
				value: nls.localize('aiAssistant.category', 'Kwaipilot'),
				original: 'Kwaipilot',
			},
			icon: Codicon.gear,
			f1: true,
			menu: {
				id: MenuId.ViewTitle,
				when: ContextKeyExpr.equals('view', AI_ASSISTANT_PANEL_ID),
				group: 'navigation',
			},
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		try {
			const commandService = accessor.get(ICommandService);
			// 打开设置页面
			await commandService.executeCommand('workbench.action.openSettings', '@ext:kuaishou.kwaipilot',);
		} catch (error) {
			console.error('Error in OpenAIAssistantSettingsAction.run', error);
		}
	}
}

// 注册所有功能 - 添加错误处理
export function registerAIAssistant(): void {
	try {
		registerAction2(AIAssistantOpenPanelAction);

		registerAction2(OpenAIAssistantSettingsAction);

		CommandsRegistry.registerCommand({
			id: 'kwaiPilotChatWebView.focus',
			handler: async (accessor: ServicesAccessor) => {
				const viewsService = accessor.get(IViewsService);
				const layoutService = accessor.get(IWorkbenchLayoutService);

				// 确保面板区域可见
				await layoutService.setPartHidden(false, Parts.PANEL_PART);

				// 打开 AI 助手视图
				const view = await viewsService.openView(AI_ASSISTANT_PANEL_ID, true);

				// 聚焦到视图
				if (view) {
					view.focus();
				}

				return true;
			}
		});

		// 注册打开侧边栏命令
		CommandsRegistry.registerCommand({
			id: 'kwaipilot.Open Kwaipilot Panel',
			handler: async (accessor: ServicesAccessor) => {
				const viewsService = accessor.get(IViewsService);
				// 打开 AI 助手视图
				await viewsService.openView(AI_ASSISTANT_PANEL_ID, true);
			}
		});

	} catch (error) {
		console.error('Error registering AI Assistant functionality', error);
	}
}

// 立即注册所有功能
registerAIAssistant();
