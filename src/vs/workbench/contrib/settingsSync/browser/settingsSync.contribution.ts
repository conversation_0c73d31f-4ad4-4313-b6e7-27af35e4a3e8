/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { localize } from '../../../../nls.js';
import { registerAction2, Action2, MenuId } from '../../../../platform/actions/common/actions.js';
import { ISettingsSyncService } from '../../../../platform/settingsSync/common/settingsSync.js';
import { ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';
import { Categories } from '../../../../platform/action/common/actionCommonCategories.js';
import { INotificationService, Severity } from '../../../../platform/notification/common/notification.js';
import { Registry } from '../../../../platform/registry/common/platform.js';
import { Extensions as WorkbenchExtensions, IWorkbenchContributionsRegistry } from '../../../../workbench/common/contributions.js';
import { LifecyclePhase } from '../../../../workbench/services/lifecycle/common/lifecycle.js';
import { ISettingsSyncProgressService } from '../../../../platform/settingsSync/common/settingsSyncProgress.js';
import { SettingsSyncProgressService } from './settingsSyncProgressService.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { SettingsSyncService } from '../../../../platform/settingsSync/common/settingsSyncService.js';
import { IExtensionsSyncService } from '../../../../platform/extensionsSync/browser/extensionsSyncService.js';
import { vscodeForkProductNameMap } from '../../../../platform/environment/common/environmentService.js';
import { IDialogService } from '../../../../platform/dialogs/common/dialogs.js';

// Register services
registerSingleton(ISettingsSyncProgressService, SettingsSyncProgressService, InstantiationType.Eager);
registerSingleton(ISettingsSyncService, SettingsSyncService, InstantiationType.Eager);

// Register as workbench contribution
Registry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench)
	.registerWorkbenchContribution(SettingsSyncProgressService, LifecyclePhase.Restored);


// Base class: Common settings import Action
abstract class BaseImportSettingsAction extends Action2 {
	constructor(
		private readonly productId: keyof typeof vscodeForkProductNameMap,
		order: number
	) {
		super({
			id: `workbench.action.import${vscodeForkProductNameMap[productId].name}Settings`,
			title: {
				value: localize('importSettings', "Import {0} Settings", vscodeForkProductNameMap[productId].name),
				original: `Import ${vscodeForkProductNameMap[productId].name} Settings`
			},
			category: Categories.Preferences,
			f1: true,
			menu: {
				id: MenuId.GlobalActivity,
				group: '6_sync',
				order: order
			}
		});
	}
	private async showPreview(accessor: ServicesAccessor): Promise<boolean> {
		const dialogService = accessor.get(IDialogService);
		const result = await dialogService.confirm({
			message: localize('preview title', "Import settings preview"),
			detail: localize('preview detail', "This will overwrite your current IDE settings and cannot be undone. Do you want to continue?"
			),
			primaryButton: localize('import', "Import"),
			cancelButton: localize('cancel', "Cancel"),
			type: Severity.Info
		});

		return result.confirmed;
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const settingsSyncService = accessor.get(ISettingsSyncService);
		const notificationService = accessor.get(INotificationService);
		const extensionsSyncService = accessor.get(IExtensionsSyncService);

		const autoCloseNotify = (params: { message: string; level: Severity; delay?: number }) => {
			const { message, level, delay = 1e3 } = params;
			const handle = notificationService.notify({ severity: level, message, });
			setTimeout(() => handle.close(), delay);
		};

		const importStatusMap = {
			setting: {
				success: false,
				msg: ''
			},
			extension: {
				success: false,
				msg: ''
			}
		};

		// Show preview
		const shouldImport = await this.showPreview(accessor);
		if (!shouldImport) {
			return;
		}

		// Import settings
		try {
			const isOk = await settingsSyncService.importSettings(this.productId);
			const isMcpOk = await settingsSyncService.importMcpSettings(this.productId);
			importStatusMap.setting.success = isOk && isMcpOk;
		} catch (error) {
			importStatusMap.setting.msg = localize('settingsImportError', "Failed to import settings: {0}", error.message);
		}

		// Import extensions
		try {
			const result = await extensionsSyncService.importExtensions(this.productId, (message) => {
				autoCloseNotify({ level: Severity.Info, message });
			});
			importStatusMap.extension.success = result.success;
			if (!result.success) {
				// When some extensions fail to import
				const failedCount = result.failedExtensions.length;
				const message = localize('extensionsImportPartiallyFailed',
					"Failed to import {1} extensions while importing {0} extensions",
					vscodeForkProductNameMap[this.productId].id,
					failedCount
				);

				const details = result.failedExtensions
					.map(ext => `${ext.id}: ${ext.error}`)
					.join('\n');

				importStatusMap.extension.msg = `${message}\n${details}`;
			}
		} catch (error) {
			importStatusMap.extension.msg = localize('extensionsImportError', "Failed to import extensions: {0}", error.message);
		}

		// Show notification

		const settingMsg = importStatusMap.setting.success ? localize('settingsImportSuccess', "Successfully imported {0} settings", vscodeForkProductNameMap[this.productId].id) : importStatusMap.setting.msg;
		const extensionMsg = importStatusMap.extension.success ? `
		${localize('extensionsImportSuccess', "Successfully imported {0} extensions", vscodeForkProductNameMap[this.productId].id)}
		${importStatusMap.extension.msg}

		` : importStatusMap.extension.msg;
		notificationService.info(`
			${settingMsg}
			${extensionMsg}
			`);
	}
}

// VS Code settings import
class ImportVSCodeSettingsAction extends BaseImportSettingsAction {
	constructor() {
		super('Code', 1);
	}
}

// Cursor settings import
class ImportCursorSettingsAction extends BaseImportSettingsAction {
	constructor() {
		super('Cursor', 2);
	}
}

// Trae settings import
class ImportTraeSettingsAction extends BaseImportSettingsAction {
	constructor() {
		super('Trae', 3);
	}
}

// Register all import Actions
registerAction2(ImportVSCodeSettingsAction);
registerAction2(ImportCursorSettingsAction);
registerAction2(ImportTraeSettingsAction);
