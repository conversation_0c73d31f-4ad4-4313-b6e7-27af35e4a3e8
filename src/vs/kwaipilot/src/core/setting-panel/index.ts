import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { LoggerManager } from "../../base/logger";
import { AgentModule } from "../agent";
import { ConfigManager, WorkspaceStateManager } from "../../base/state-manager";
import { Config, WorkspaceState } from "../../base/state-manager/types";
import { getNonce } from "../../utils/getNonce";
import { Bridge } from "@bridge";
import { createExtensionRpcContext } from "../../base/bridge/ExtensionRpcContext";
import { NATIVE_BRIDGE_EVENT_NAME, WEBVIEW_BRIDGE_EVENT_NAME } from "shared/lib/bridge";
import { IRPCProtocol } from "shared/lib/bridge/proxyIdentifier";
import { ExtensionSettingsShape } from "shared/lib/bridge/protocol";
import { SettingPage } from "shared/lib/customSettingPanel";

export class SettingPanelModule extends CoreModule implements ExtensionSettingsShape {
  private readonly loggerScope = "SettingPanelModule";
  private panel: vscode.WebviewPanel | undefined;
  rpcContext: IRPCProtocol;
  constructor(readonly ext: ContextManager) {
    super(ext);
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.openCodeIndexManagement", () => {
        this.openCustomSettingPanel("fileIndex");
      }),
      vscode.commands.registerCommand("kwaipilot.openRulesManagement", () => {
        this.openCustomSettingPanel("rules");
      }),
      vscode.commands.registerCommand("kwaipilot.openMCPManagement", (query) => {
        this.openCustomSettingPanel("mcp", query);
      }),
    );
    this.rpcContext = createExtensionRpcContext({
      logger: () => this.getBase(LoggerManager),
      protocol: {
        onMessage: (listener) => {
          return this.getBase(Bridge).registerOneWayMessageHandler(NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE, (data, source) => {
            if (source !== this.panel?.webview) {
              return;
            }
            listener(data);
          });
        },
        send: (message) => {
          const webview = this.panel?.webview;
          if (!webview) {
            throw new Error("no webview");
          }
          this.getBase(Bridge).postOneWayMessage(webview, WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE, message);
        },
      },
    });
  }

  $openSettings(activePage?: SettingPage, query?: string) {
    this.openCustomSettingPanel(activePage, query);
  }

  openCustomSettingPanel(activePage: SettingPage = "fileIndex", query?: string) {
    this.workspaceState.update(WorkspaceState.ACTIVE_SETTING_PAGE, activePage);
    if (this.panel) {
      this.panel.webview.html = this._getHtmlForWebview(this.panel.webview, this.context.extensionUri, query);
      this.panel.reveal();
      return;
    }
    this.panel = vscode.window.createWebviewPanel(
      "kwaipilot-setting-panel",
      "Kwaipilot 设置",
      {
        viewColumn: vscode.ViewColumn.One,
        preserveFocus: true,
      },
      {
        enableScripts: true,
        retainContextWhenHidden: true,
      },
    );

    // 设置图标
    const iconPath = {
      light: vscode.Uri.joinPath(this.context.extensionUri, "resources", "light", "settings-gear.svg"),
      dark: vscode.Uri.joinPath(this.context.extensionUri, "resources", "dark", "settings-gear.svg"),
    };
    this.panel.iconPath = iconPath;

    // 监听面板关闭事件
    this.panel.onDidDispose(
      () => {
        this.panel = undefined;
      },
      null,
      this.context.subscriptions,
    );

    const webview = this.panel.webview;
    // 监听 WebView 消息
    webview.onDidReceiveMessage((message) => {
      if (message.protocol === "callHandler") {
        this.getBase(Bridge).callNativeHandler(
          webview,
          message.name,
          message.data,
          message.callbackId,
        );
      }
      else if (message.protocol === "callback") {
        this.getBase(Bridge).handleCallback(message.callbackId, message.data);
      }
      else if (message.protocol === "message") {
        this.getBase(Bridge).handleOneWayMessage(webview, message.data);
      }
    });
    // 设置 WebView 内容
    this.panel.webview.html = this._getHtmlForWebview(this.panel.webview, this.context.extensionUri, query);
  }

  /**
     * 获取webView视图
     * @param webview webView组件
     * @returns
     */
  private _getHtmlForWebview(
    webview: vscode.Webview,
    extensionUri: vscode.Uri,
    query?: string,
  ) {
    const inDevelopmentMode
      = this.context.extensionMode === vscode.ExtensionMode.Development;

    const vscMediaUrl = webview
      .asWebviewUri(vscode.Uri.joinPath(extensionUri, "setting-ui/assets"))
      .toString();

    const jsUrl = webview
      .asWebviewUri(vscode.Uri.joinPath(extensionUri, "bridge/index.js"))
      .toString();

    let scriptUri: string;
    let styleMainUri: string;
    if (!inDevelopmentMode) {
      scriptUri = webview
        .asWebviewUri(
          vscode.Uri.joinPath(extensionUri, "setting-ui/build/assets/index.js"),
        )
        .toString();
      styleMainUri = webview
        .asWebviewUri(
          vscode.Uri.joinPath(extensionUri, "setting-ui/build/assets/index.css"),
        )
        .toString();
    }
    else {
      scriptUri = "http://localhost:5174/src/index.tsx";
      styleMainUri = "http://localhost:5174/src/App.css";
    }
    this.logger.info("init webview", "webview", {
      value: {
        scriptUri,
        styleMainUri,
        vscMediaUrl,
      },
    });
    const nonce = getNonce();
    const currentTheme = vscode.window.activeColorTheme;
    const isLight = currentTheme?.kind === 1 || currentTheme?.kind === 4;
    const proxyUrl = this.getBase(ConfigManager).get(Config.PROXY_URL);

    return `<!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <link href="${styleMainUri}" rel="stylesheet">
          <script>window.vscMediaUrl = "${vscMediaUrl}"</script>
          <script>window.proxyUrl = "${proxyUrl}"</script>
          <script>window.ide = "vscode"</script>
          <script>window.colorThemeName = "dark"</script>
          <script>window.uriQuery = "${query}"</script>
          <script nonce="${nonce}" src="${jsUrl}"></script>
          <title>Continue</title>
        </head>
        <body ${!isLight ? "class='dark'" : ""}>
          <div id="root"></div>
          ${inDevelopmentMode
              ? `<script type="module">
            import RefreshRuntime from "http://localhost:5174/@react-refresh"
            RefreshRuntime.injectIntoGlobalHook(window)
            window.$RefreshReg$ = () => {}
            window.$RefreshSig$ = () => (type) => type
            window.__vite_plugin_react_preamble_installed__ = true
            </script>
            <script type="module">
              import { createHotContext } from "http://localhost:5174/@vite/client"
              window.__vite_hot_context__ = createHotContext()
            </script>`
              : ""
          }
          <script type="module" nonce="${nonce}" src="${scriptUri}"></script>
        </body>
      </html>`;
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get agent() {
    return this.getCore(AgentModule);
  }

  private get workspaceState() {
    return this.getBase(WorkspaceStateManager);
  }
}
